view {
  box-sizing: border-box;
}

.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-border {
  background-color: rgba(0, 0, 0, 0.33);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 56px;
  position: relative;
}

.tab-bar-item image {
  width: 24px;
  height: 24px;
  margin-bottom: 3px;
}

.tab-bar-item view {
  font-size: 12px;
}

.tab-bar-search {
  width: 66px;
  height: 66px;
  position: absolute;
  top: 80px;
  left: 50%;
  transform: translate(-50%, 0px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  overflow: hidden;

  image {
    width: 54px;
    height: 54px;
    background: white;
    margin-bottom: 0px;
    border-radius: 54px;
    border: 3px solid white;
  }
}
//只调用
.nut-popover {
  display: inline-block;
  position: absolute;
  top: -109px;
  width: 100px;
}
.nut-popover .popover-content--top {
  width: 164px;
}
.nut-popover .popover-content .popover-menu-item {
  height: 40px !important;
  color: white;
}
.nut-popover .popover-content {
  background-color: #4dbcb4;
}
.nut-popover .popover-arrow-top {
  border-top-color: #4dbcb4;
}

.nut-popover .popover-content .popover-menu-item .popover-menu-item-name {
  font-size: 17px !important;
}
