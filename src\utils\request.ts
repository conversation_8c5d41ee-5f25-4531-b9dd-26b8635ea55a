import Taro, {
  request,
  showToast,
  getStorageSync,
  reLaunch,
} from '@tarojs/taro';
import { BASE_URL } from '@/config/index';
import _ from 'lodash';

// 去掉null 等等空值
function filterEmptyConty(data: any) {
  for (const key in data) {
    if (data[key] instanceof Object) {
      filterEmptyConty(data[key]);
    } else if (data[key] instanceof Array) {
      data[key].map((item: any) => {
        filterEmptyConty(item);
      });
    } else {
      if (!data[key] && data[key] !== 0) {
        delete data[key];
      }
    }
  }
}

type methodType =
  | 'OPTIONS'
  | 'GET'
  | 'HEAD'
  | 'POST'
  | 'PUT'
  | 'DELETE'
  | 'TRACE'
  | 'CONNECT'
  | undefined;

interface RequesConfig {
  header?: any;
  filterEmpty?: boolean;
}

//网络请求统一处理
export default <P, D>({
  url,
  method = 'POST',
  data,
  RequesConfig,
}: {
  url: string;
  method: methodType;
  data: P;
  RequesConfig?: RequesConfig;
}) => {
  const { token: Authorization = '' } = getStorageSync('UserInfo');
  return new Promise((relose, reject) => {
    const params = data;

    // 是否把控制去掉
    if (!RequesConfig?.filterEmpty) {
      filterEmptyConty(params);
    }

    request({
      url: BASE_URL + url,
      method,
      data: params,
      header: {
        'content-type': 'application/json', // 默认值
        Authorization,
      },

      complete: function (res: any) {
        if (res?.code === 401) {
          // reLaunch({
          //     url: '/pages/login/login'
          // })
          reject('登录已过期!');
          return;
        } else if (_.get(res, 'data.code') !== 200) {
          if (_.get(res, 'data.msg') === '缺少必要的请求参数: phone') return;
          if (_.get(res, 'data.msg') === '缺少必要的请求参数: devicesn') return;
          if (_.get(res, 'data.msg') === '当前设备已经绑定，请勿重复绑定！')
            return;
          showToast({
            title: _.get(res, 'data.msg') || '网络请求失败!',
            icon: 'none',
            duration: 2000,
          });
          reject(res);
          return;
        }
        relose(res.data);
      },
      fail: function (error) {
        reject(error);
      },
    });
  });
};
