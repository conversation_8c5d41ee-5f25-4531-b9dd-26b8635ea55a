page {
  padding-bottom: 50px;
  background-color: #f6f6f6;
}
.home-page {
  padding-bottom: 82px;
  position: relative;

  &-header {
    width: 375px;
    border-radius: 0px;
    padding-bottom: 38rpx;
  }

  //进度条
  .CircleProgress {
    position: absolute;
    width: 100%;
    height: 78vh;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    z-index: 2001;
    .CircleProgressHint {
      width: 100%;
      color: white;
      text-align: center;
      position: relative;
      top: -200px;
    }
  }

  .slidePngStyle {
    width: 30px;
    height: 30px;
  }
  .slidePngStyle1 {
    width: 45px;
    height: 45px;
  }

  &-quantity {
    width: 160px;
    height: 160px;
    border-radius: 160px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;

    &-circle {
      border-radius: 100%;
    }

    &-t1 {
      font-size: 14px;
      font-weight: 600;
      color: #999999;
      // color:#f2f7ffaa
    }
    &-t3 {
      font-size: 14px;
      font-weight: 600;
      color: #999999;
      display: inline;
      position: absolute;
      bottom: 3px;
      // color:#f2f7ffaa
    }
    &-t1 {
      display: flex;
      justify-content: center;
    }

    &-t2 {
      font-size: 25px;
      color: #20355d;
      position: relative;
    }

    &-t4 {
      font-size: 18px;
      font-weight: 800;
    }
  }

  &-rowbtn {
    width: 90%;
    height: 8vh;
    background: white;
    margin-left: 5%;
    border-radius: 40rpx;
    margin-top: 2%;
    display: flex;
    justify-content: space-between;

    &-btn1 {
      width: 45%;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    &-btn2 {
      width: 45%;
      height: 50rpx;
      padding: 2px;
      font-size: 13px;
      float: left;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: transparent;
      border-radius: 4px;
      color: #ccc;
    }
    .btn2List {
      width: 25%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  &-row {
    width: 100%;
    // height: 436px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 18px;
  }

  &-record {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 90%;
    height: 27vh;
    margin-top: 6%;
    padding: 4%;
    background: white;
    border-radius: 20px;

    &-row:nth-child(1),
    &-row:nth-child(3) {
      border-right: 1px solid #f6f6f6;
    }
    &-row:nth-child(1),
    &-row:nth-child(2) {
      border-bottom: 1px solid #f6f6f6;
    }
    &-row:nth-child(1) {
      image {
        position: relative;
        top: -40rpx;
        left: 27rpx;
      }
      .RowContent {
        position: relative;
        top: 10px;
        left: 35px;
      }
    }

    &-row:nth-child(2) {
      image {
        margin-right: 36rpx;
        position: relative;
        top: -40rpx;
        left: 57rpx;
      }
      .RowContent {
        position: relative;
        top: 10px;
        left: 35px;
      }
    }

    &-row:nth-child(3) {
      image {
        position: relative;
        top: -20rpx;
        left: 27rpx;
      }
      .RowContent {
        position: relative;
        top: 40rpx;
        left: 35px;
      }
    }

    &-row:nth-child(4) {
      image {
        width: 60rpx;
        height: 60rpx;
        margin-right: 36rpx;
        top: -20rpx;
        left: 57rpx;
      }
      .RowContent {
        position: relative;
        top: 40rpx;
        left: 35px;
      }
    }

    &-row {
      display: flex;
      width: 45%;
      height: 11vh;
      background: white;
      justify-content: left;
      align-items: center;
      width: 47%;

      &-img {
        width: 60rpx;
        height: 60rpx;
        position: relative;
        top: -20px;
        left: -9px;
      }

      &-label {
        font-size: 38rpx;
        font-family: ui-rounded;
      }

      &-value {
        font-size: 15px;
        font-weight: 500;
        position: relative;
        top: -36rpx;
      }
    }
  }

  &-content {
    background: #ffffff;
    border-radius: 28px;
    width: 90%;
    margin-left: 5%;
    padding: 25rpx;
    display: flex;
    justify-content: space-around;
    height: 15vh;

    &-cell {
      display: flex;
      align-items: center;
      gap: 10px;
      &-row {
        margin-top: 10px;
      }
    }

    &-text {
      font-size: 16px;
      color: white;
    }

    &-img {
      width: 45rpx;
      height: 45rpx;
      margin-top: 8rpx;
      // background-color: #172D45;
      // color: white;
    }

    .lanya {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-switch {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      width: 90%;
      background-color: white;
      border-radius: 20px;
      margin-left: 5%;
      padding: 5%;

      // &-row:nth-child(1){
      //   position: absolute;
      //   top: 35%;
      //   right: 5%;
      // }

      // &-row:nth-child(2){
      //   .titleBtn{
      //     width: 100%;
      //     height: 150rpx;
      //     display: flex;
      //     align-items: center;
      //     justify-content: center;
      //     border-radius: 50%;
      //   }
      //   image{
      //     width: 75rpx;
      //     height: 75rpx;
      //     margin-left: 0px;
      //   }
      // }

      // &-row:nth-child(3){
      //   image{
      //     margin-left:0px;
      //   }
      // }

      &-row {
        width: 21%;
        background-color: #f3f5f9;
        height: 126rpx;
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;

        //智能加热
        .smartWarm {
          position: relative;
          flex-wrap: wrap;
          font-size: 15px;
          width: 60%;
          height: 150rpx;
          display: flex;
          justify-content: space-around;
          align-items: center;
          padding-right: 21px;
          .box {
            display: flex;
          }
        }
        .CircleProgress1 {
          position: absolute;
          z-index: 10;
          color: red;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .titleFont {
          width: 100%;
          text-align: center;
          font-size: 12px;
          color: #ccc;
        }
        image {
          width: 75rpx;
          height: 75rpx;
          // margin-left: 24%;
        }
      }
    }
  }
}

.lockInTime {
  margin-bottom: 0%;
  color: rgba(43, 43, 43, 0.631372549);
  margin-left: 5%;
  font-size: 28rpx;
  margin-top: 3%;
}

.lanya1 {
  width: 33rpx;
  height: 33rpx;
}

.tag {
  width: 100%;
  text-align: right;
}
.GotoBing {
  font-size: 27rpx;
  color: blue;
  margin-left: 10rpx;
  &:hover {
    color: rgb(0, 128, 255);
  }
}
.BlueGotoBing {
  font-size: 25rpx;
  color: white;
  margin-left: 60rpx;
  line-height: 44rpx;
  border: 2rpx solid rgb(196, 18, 18);
  height: 44rpx;
  width: 82rpx;
  background: rgb(232, 57, 57);
  text-align: center;
  border-radius: 8rpx;
  &:active {
    background: rgb(255, 2, 2);
  }
}

.hintHurdle {
  display: flex;
  &-tittle {
    padding-left: 5px;
  }
}

.nut-cell {
  background: transparent;
}

.btnContent {
  margin: 4%;
  margin-left: 10%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  .userName {
    font-size: 3.6vw;
    font-weight: 600;
  }
  .EquIdCode {
    color: rgba(43, 43, 43, 0.631372549);
    width: 100px;
    height: 18px;
    position: relative;
    font-size: 3.6vw;
    margin-top: 5%;
    Image {
      width: 25rpx;
      height: 25rpx;
      position: absolute;
      bottom: 0px;
      right: -20px;
    }
  }
}
@keyframes mydongman {
  0% {
    color: rgb(192, 255, 219);
  }
  50% {
    color: rgb(0, 255, 200);
  }
  100% {
    color: blue;
  }
}
//低温
.lowTem {
  animation: mydongman 1s linear 0.2s infinite;
  font-size: 38rpx;
  font-family: ui-rounded;
}

@keyframes mydongman1 {
  0% {
    background-color: rgb(192, 255, 219);
  }
  50% {
    background-color: rgb(131, 249, 255);
  }
  100% {
    background-color: rgb(137, 202, 255);
  }
}

//低压
.lowTension {
  width: 100%;
  height: 150rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  animation: mydongman1 1s linear 0.2s infinite;
}
//正常
.normal {
  // text-align: center;
  font-size: 38rpx;
  font-family: ui-rounded;
}

.batteryIcon {
  width: 30px;
  height: 30px;
  image {
    width: 38rpx;
    height: 38rpx;
  }
}
.titleFont1 {
  width: 21%;
  text-align: center;
  font-size: 14px;
  margin-top: 15px;
}
.titleFont2 {
  width: 57%;
  text-align: center;
  font-size: 14px;
  margin-top: 15px;
}
.titleBtn {
  width: 100%;
  height: 150rpx;
  display: flex;
  // flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.equList {
  .nut-dialog__outer {
    width: 85%;
    display: flex;
    justify-content: center;
  }
  .nut-dialog {
    width: 100%;
  }
}

.switchList {
  display: flex;
  -webkit-justify-content: space-around;
  -ms-flex-pack: distribute;
  justify-content: space-between;
  align-items: center;
  border-radius: 21rpx;
  height: 80rpx;
  padding: 20rpx;
  margin: 20rpx 2rpx;
  .userInfo {
    width: 79%;
    .deviceNo {
      text-align: left;
      padding-left: 4%;
    }
    .Name {
      color: black;
      font-weight: 600;
      text-align: left;
      padding: 1px;
    }
  }
  .onUnbindBtn {
    width: 21%;
    height: 182%;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    z-index: 100;
  }
  Image {
    width: 20px;
    height: 20px;
  }
}

.BindingSwitchList {
  background-color: #e4f8f6;
}
.TopzIndex {
  position: relative;
  z-index: 1201;
}

.Overlay1 {
  width: 32%;
  height: 15%;
  z-index: 11;
  position: absolute;
  left: 0%;
  bottom: 12%;
}
//正常滑动关机
.rightClose {
  width: 30px;
  position: absolute;
  right: 17%;
  top: 18%;
}
.leftClose {
  width: 30px;
  position: absolute;
  left: 11%;
  top: 18%;
}
.appleClose {
  color: white;
  width: 224px;
  height: 9.7vh;
  border-radius: 41px;
  line-height: 9.7vh;
  text-align: center;
  font-size: 31rpx;
  background-color: #fdb189;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
//二次苹果关机
.close1 {
  color: white;
  width: 120rpx;
  height: 120rpx;
  background-color: #8e8e8e69;
  position: relative;
  top: 68%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.appleCloseTitle {
  color: #465d77e9;
  line-height: 23px;
  font-size: 13px;
  text-align: center;
}
.appleCloseTitle2 {
  position: relative;
  top: 66%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #646464e2;
}

.NoticeBarPosition {
  width: 90%;
}
.linearText {
  animation: textLinear 1s linear 0.2s infinite;
  -webkit-background-clip: text;
  color: transparent;
  margin-left: 22%;
}

@keyframes textLinear {
  0% {
    background-image: linear-gradient(
      to right,
      white 20%,
      rgb(88, 88, 88) 40%,
      rgb(88, 88, 88) 100%
    );
  }
  12% {
    background-image: linear-gradient(
      to right,
      white 30%,
      rgb(88, 88, 88) 50%,
      rgb(88, 88, 88) 100%
    );
  }
  24% {
    background-image: linear-gradient(
      to right,
      white 40%,
      rgb(88, 88, 88) 60%,
      rgb(88, 88, 88) 100%
    );
  }
  36% {
    background-image: linear-gradient(
      to right,
      rgb(88, 88, 88) 10%,
      white 50%,
      rgb(88, 88, 88) 70%
    );
  }
  48% {
    background-image: linear-gradient(
      to right,
      rgb(88, 88, 88) 20%,
      white 60%,
      rgb(88, 88, 88) 80%
    );
  }
  60% {
    background-image: linear-gradient(
      to right,
      rgb(88, 88, 88) 30%,
      white 70%,
      rgb(88, 88, 88) 90%
    );
  }
  72% {
    background-image: linear-gradient(
      to right,
      rgb(88, 88, 88) 10%,
      rgb(88, 88, 88) 40%,
      white 80%
    );
  }
  86% {
    background-image: linear-gradient(
      to right,
      rgb(88, 88, 88) 20%,
      rgb(88, 88, 88) 50%,
      white 90%
    );
  }
  100% {
    background-image: linear-gradient(
      to right,
      rgb(88, 88, 88) 30%,
      rgb(88, 88, 88) 60%,
      white 100%
    );
  }
}
//组件样式
.nut-circleprogress-text {
  color: white;
}
.nut-circleprogress-text {
  color: black;
}
.nut-overlay {
  background: #f9fbffe4;
}
