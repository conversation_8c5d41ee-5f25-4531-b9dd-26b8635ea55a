import React, { useEffect, useState, useCallback } from 'react';
import Taro from '@tarojs/taro';
import { Icon } from '@nutui/nutui-react-taro';
import { navigateTo } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { Dialog } from '@nutui/nutui-react-taro';
import refreshStore from '../../../store/refreshStore';

import {
  getBladeSystemUserValidatePhone,
  postBladeSystemUpBindEqu,
  postBladeSystemUpUnBindEqu,
  getBladeSystemUserCkEquNoExist,
  userBindDevice,
  getBatterAll,
  liftUserBindEqu,
} from '../../../api/index';
import {
  Cell,
  CellGroup,
  Input,
  Collapse,
  CollapseItem,
} from '@nutui/nutui-react-taro';
import './index.scss';

function myDevicePage() {
  const [isButtonVisible, setIsButtonVisible] = useState(false);
  const [equNo, setEquNo] = useState('');
  const [visible1, setVisible1] = useState(false);
  const [userId, setUserId] = useState('');
  const [isBind, setisBind] = useState(false);
  const [errorText1, seterrorText1] = useState('');
  const [form, setForm] = useState({
    seqNo: '',
    name: '',
    phone: '',
    carNo: '无',
    id: '',
  });
  const [equList, setEquList] = useState<any[]>([]);

  //解绑
  const onUnbind: any = () => {
    setVisible1(false);
    postBladeSystemUpUnBindEqu(form)
      .then(res => {
        Taro.removeStorageSync('seqNo');
        setIsButtonVisible(false);
        refreshStore.triggerRefresh();
        refreshStore.triggerDeviceSwitch();
      })
      .then(() => {
        removeUserBindDevice(form);
        loading();
      });
  };

  //绑定
  const onBind: any = seqNo => {
    if (seqNo) {
      //切换
      postUpBindEqu({ id: userId, seqNo: seqNo });
    } else {
      //效验
      if (form) {
        const regex = /^BAT[A-Z0-9]{8}$/;
        if (!regex.test(form.seqNo)) {
          seterrorText1('格式错误：前三位必须为BAT，后八位必须为数字英文');
          return;
        }
        if (form.seqNo === '') {
          seterrorText1('电池编号不能为空！！');
          return;
        }
      }
      //添加
      postUpBindEqu1(form);
    }
  };

  //切换设备
  const switchEqu: any = seqNo => {
    onBind(seqNo);
  };

  const handleChange = (name, value) => {
    console.log(name, value);
    if (name == 'equNo') {
      setEquNo(value);
    }
    if (name == 'seqNo') {
      const regex = /^BAT[A-Z0-9]{8}$/;
      regex.test(value)
        ? seterrorText1('')
        : seterrorText1('格式错误：前三位必须为BAT，后八位必须为数字英文');
      if (value === '') seterrorText1('电池编号不能为空！！');
    }
    setForm({
      ...form,
      [name]: value,
    });
  };

  //弹窗
  const unbind = () => {
    setVisible1(true);
  };

  const isMatchDateis = useCallback(() => {
    let seqNo = Taro.getStorageSync('seqNo');
    if (seqNo) {
      setIsButtonVisible(true);
    } else {
      setIsButtonVisible(false);
    }
    let phone = Taro.getStorageSync('phone');
    getBladeSystemUserValidatePhone({ phone: phone }).then(res => {
      const data = res.data;
      console.log(data, '设备数据');
      setUserId(data.id);
      setForm({
        ...data,
      });
      if (data.seqNo !== '') {
        console.log('用户绑定设备');
        getBatterAll({ userId: data.id }).then(res => {
          if (res.data.length == 0) {
            addUserBindDevice1({ userId: data.id, seqNo: seqNo });
            loading();
          }
        });
      }
      getBindBatterList(data.id);
      if (data.seqNo) {
        Taro.setStorageSync('seqNo', data.seqNo);
        setIsButtonVisible(true);
      }
      setEquNo('');
    });
  }, []);

  //切换
  const postUpBindEqu = form => {
    postBladeSystemUpBindEqu(form).then(res => {
      console.log(res.data);
      if (res.msg == '设备号已经绑定，请重新输入设备号') return;
      setForm({
        ...res.data,
      });
      Taro.setStorageSync('seqNo', res.data.seqNo);

      refreshStore.triggerRefresh();
      refreshStore.triggerDeviceSwitch();
      loading();
    });
  };
  //添加
  const postUpBindEqu1 = form => {
    userBindDevice({ userId: userId, deviceNo: form.seqNo }).then(resTwo => {
      console.log('新增成功?', resTwo);
      //验证1
      if (resTwo.msg == '当前设备已经被绑定') return;
      postBladeSystemUpBindEqu(form)
        .then(res => {
          console.log(res.data);
          setForm({
            ...res.data,
          });
          Taro.setStorageSync('seqNo', res.data.seqNo);
          refreshStore.triggerRefresh();
          refreshStore.triggerDeviceSwitch();
          loading();
        })
        .catch(err => {
          if (err.data.msg == '设备号已经绑定，请重新输入设备号') {
            liftUserBindEqu({ userId: userId, deviceNo: form.seqNo }).then(
              res => {
                console.log(res, '删除成功？');
              }
            );
          }
        });
    });
  };

  //新增列表
  const addUserBindDevice = form => {
    userBindDevice({ userId: userId, deviceNo: form.seqNo }).then(res => {
      console.log('新增成功?', res);
    });
  };
  //自增
  const addUserBindDevice1 = form => {
    userBindDevice({ userId: form.userId, deviceNo: form.seqNo }).then(res => {
      console.log('新增成功?', res);
    });
  };
  //获取列表
  const getBindBatterList = id => {
    getBatterAll({ userId: id }).then(res => {
      setEquList(res.data);
    });
  };
  //删除列表
  const removeUserBindDevice = form => {
    liftUserBindEqu({ userId: userId, deviceNo: form.seqNo }).then(res => {
      console.log('删除成功？', res);
    });
  };
  //刷新数据
  const loading = () => {
    let phone = Taro.getStorageSync('phone');
    getBladeSystemUserValidatePhone({ phone: phone }).then(res => {
      setEquNo('');
      const data = res.data;
      getBindBatterList(data.id);
      setForm({
        ...data,
      });
      if (data.seqNo) {
        Taro.setStorageSync('seqNo', data.seqNo);
      }
    });
  };

  const tapScan = async () => {
    Taro.scanCode({
      success: function (res) {
        const result = res.result;
        const response = getBladeSystemUserCkEquNoExist({ equNo: res.result });
        response.then(status => {
          if (status.success) {
            console.log(result);
            // handleChange('seqNo', result)
            handleChange('equNo', result);
          }
        });
      },
    });
  };

  useEffect(() => {
    isMatchDateis();
  }, [isMatchDateis]);

  return (
    <View className='myDevice-page'>
      <Dialog
        title='提示'
        visible={visible1}
        onOk={() => onUnbind()}
        onCancel={() => setVisible1(false)}
      >
        是否确定解绑？
      </Dialog>
      <CellGroup className='myDevice-page-CellGroup'>
        <Cell
          key='seqNo'
          desc={Taro.getStorageSync('seqNo')}
          title='当前设备'
        />
        <Collapse
          activeName={['1', '2']}
          icon='arrow-down'
          iconSize='16'
          iconColor='#999'
        >
          <CollapseItem title='切换设备' name='1'>
            {equList.map((device, index) => {
              const isCurrentDevice =
                device.deviceNo === Taro.getStorageSync('seqNo');
              console.log(isCurrentDevice);
              if (isCurrentDevice) {
                return (
                  <Cell
                    key={index}
                    title={device.deviceNo}
                    linkSlot={
                      <View>
                        <View className='onUnbindBtn' onClick={() => unbind()}>
                          解绑
                        </View>
                      </View>
                    }
                  />
                );
              } else {
                return (
                  <Cell
                    key={index}
                    title={device.deviceNo}
                    linkSlot={
                      <View>
                        <View
                          className='switchBtn'
                          onClick={() => switchEqu(device.deviceNo)}
                        >
                          切换
                        </View>
                      </View>
                    }
                  />
                );
              }
            })}
            {/* 设备添加 */}
            <View className='addEqu'>
              {
                <Input
                  label='电池编号'
                  placeholder='请输入电池编号'
                  clearable
                  onChange={e => handleChange('seqNo', e)}
                  errorMessage={errorText1}
                  defaultValue={equNo}
                  slotButton={
                    <Icon onClick={tapScan} color='#000000' name='scan2'></Icon>
                  }
                />
              }
              <View className='onUnbindBtn' onClick={() => onBind()}>
                添加
              </View>
            </View>
          </CollapseItem>
        </Collapse>
        <Cell key='name' desc={form['name']} title='用户名' />
        <Cell key='phone' desc={form['phone']} title='手机号' />
        <Cell key='carNo' desc={form['carNo']} title='车牌号' />
      </CellGroup>
    </View>
  );
}

export default myDevicePage;
