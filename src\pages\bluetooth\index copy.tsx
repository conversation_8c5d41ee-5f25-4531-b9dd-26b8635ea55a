import React, { useRef, useState } from 'react';
import Taro from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { Button, SearchBar, Cell, Icon } from '@nutui/nutui-react-taro';
import { inject, observer } from 'mobx-react';

import './index.scss';

const BluetoothPage = observer(({ store }) => {
  const state = useRef({
    deviceId: '',
    characteristicId: '',
    serviceId: '',
    writeDefaultId: '',
    readId: '',
    localName: '未知',
  });

  const [dataSource, setDataSource] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const handleBegin = () => {
    setLoading(true);
    openBluetoothAdapter();
  };

  const getBLEDeviceServices = deviceId => {
    console.log('getBLEDeviceServices', deviceId);
    // let serviceId;
    Taro.getBLEDeviceServices({
      deviceId,
      success: res => {
        const uuid = res.services.find(
          item =>
            !item.uuid.includes('00001800') && !item.uuid.includes('00001800')
        );
        console.log(uuid);
        getBLEDeviceCharacteristics(deviceId, res.services[0].uuid);
      },
    });
  };

  const stopBluetoothDevicesDiscovery = () => {
    Taro.stopBluetoothDevicesDiscovery({
      success(res) {
        console.log('停止搜索');
        console.log(res);
      },
    });
    Taro.onBLEConnectionStateChange(function (res) {
      // 该方法回调中可以用于处理连接意外断开等异常情况
      if (!res.connected) {
        console.log('蓝牙已经断开----');
        Taro.showToast({
          title: '蓝牙已经断开!',
        });
      }
    });
  };

  const getBLEDeviceCharacteristics = (deviceId, serviceId) => {
    Taro.getBLEDeviceCharacteristics({
      deviceId,
      serviceId,
      success: res => {
        console.log('getBLEDeviceCharacteristics');
        console.log(res);
        // const result = res.characteristics[0]
        //writeDefault  写
        //read 读
        const writeRelust = res.characteristics.find(
          value => value.properties.write
        );
        const readRelust = res.characteristics.find(
          value => value.properties.read
        );
        console.log(writeRelust);
        console.log(readRelust);
        if (writeRelust && readRelust) {
          state.current = {
            ...state.current,
            serviceId,
            writeDefaultId: writeRelust.uuid,
            readId: readRelust.uuid,
          };
          store.counterStore.setState(state.current);
          Taro.switchTab({
            url: '/pages/home/<USER>',
          });
        }
      },
      fail(err) {
        console.log(err, '获取失败');
      },
    });
  };

  const createBLEConnection = items => {
    const { deviceId } = items;
    Taro.showLoading({
      title: '连接中...',
    });
    Taro.createBLEConnection({
      deviceId,
      success: res => {
        Taro.hideLoading();
        stopBluetoothDevicesDiscovery(); // 连接成功，停止搜索
        console.log(res, '连接成功');
        if (res.errCode == 0) {
          state.current = {
            ...state.current,
            deviceId,
            localName: items.localName || items.name,
          };
          getBLEDeviceServices(deviceId); // 获取服务
        } else if (res.errCode == 10012) {
          Taro.showToast({
            title: '连接超时，请重试！',
          });
        }
      },
      fail(error) {
        Taro.hideLoading();
        console.log(error, '连接失败');
        Taro.showToast({
          title: '连接失败！',
        });
      },
    });
  };
  const watchBluetoothFound = () => {
    Taro.onBluetoothDeviceFound((res: any) => {
      // 监听搜索到的新设备
      const relust: any = [];
      const devices = res.devices;
      console.log(res);
      console.log('获取新的列表');
      // devices.map((items: any) => {
      //   const name = items.localName || items.name
      //   if (name.includes('BAT') || name.includes('BLE-')) {
      //     relust.push(items)
      //   }
      // })
      // setDataSource([...dataSource, ...relust])
    });
  };

  const getBluetoothDevices = () => {
    setTimeout(() => {
      Taro.getBluetoothDevices({
        // 获取搜索到的设备
        success: (res: any) => {
          console.log(res, '搜索到的设备');
          console.log(res.devices);
          if (res.devices.length > 0) {
            let devicesListArr: any = res.devices.filter(item => {
              const name = item.localName || item.name;
              return name.includes('BAT') || name.includes('BLE-');
            });

            Taro.hideLoading();
            watchBluetoothFound();
            setDataSource(devicesListArr);
          } else {
            Taro.hideLoading();
            Taro.showModal({
              title: '温馨提示',
              content: '无法搜索到蓝牙设备，请重试',
              showCancel: false,
            });
            Taro.closeBluetoothAdapter({
              //关闭蓝牙模块
              success: res => {
                console.log(res, '关闭蓝牙模块');
              },
            });
          }
        },
      });
    }, 2000);
  };

  const searchBlue = () => {
    Taro.startBluetoothDevicesDiscovery({
      // services: [],
      success: res => {
        console.log(res, '开始搜索设备');
        Taro.showLoading({
          title: '正在搜索设备',
        });
        getBluetoothDevices();
      },
      fail: res => {
        console.log(res, '搜索失败');
        Taro.showToast({
          title: '搜索蓝牙设备失败!',
          icon: 'none',
        });
      },
    });
  };

  const openBluetoothAdapter = () => {
    Taro.openBluetoothAdapter({
      //初始化蓝牙模块
      success(res) {
        console.log(res, '初始化蓝牙成功');
        Taro.onBluetoothAdapterStateChange(res => {
          // 监听蓝牙适配器状态变化
          if (!res.available) {
            Taro.showModal({
              title: '温馨提示',
              content: '蓝牙适配器不可用，请重新启动',
              showCancel: false,
            });
          }
        });
        searchBlue(); //开始搜索
      },
      fail(res) {
        setLoading(false);
        console.log(res, '初始化蓝牙失败');
        Taro.showToast({
          title: '请检查手机蓝牙是否打开',
          icon: 'none',
        });
      },
    });
  };

  const handleStop = () => {
    setLoading(false);
  };

  const CloseBLEConnection = () => {
    Taro.closeBLEConnection({
      deviceId: state.current.deviceId,
      success: () => {
        Taro.showToast({
          title: '已断开连接',
        });
        setLoading(false);
        setDataSource([]);
      },
    });
    closeBluetoothAdapter();
  };

  const closeBluetoothAdapter = () => {
    Taro.closeBluetoothAdapter({
      success: res => {
        console.log(res);
      },
    });
  };

  return (
    <View className='bluetooth-page'>
      <View className='bluetooth-page-search'>
        <View className='bluetooth-page-search-block'>
          <View>{loading ? '寻找中' : '等待寻找'}</View>
          {loading && <View className='scanning'></View>}
        </View>
      </View>
      <View className='bluetooth-page-botton'>
        <Button loading={loading} onClick={handleBegin} type='info'>
          寻找电池
        </Button>
        {loading && (
          <Button
            className='bluetooth-page-botton-right'
            onClick={handleStop}
            type='warning'
          >
            停止寻找
          </Button>
        )}
      </View>
      <View className='bluetooth-page-body'>
        <Cell>
          <View className='bluetooth-page-body-block'>
            <View className='bluetooth-page-body-title'>
              已发现个
              <View className='bluetooth-page-body-text'>
                {dataSource.length}
              </View>
              蓝牙设备
            </View>

            <SearchBar placeholder='请输入关键字搜索设备' />
            <View className='bluetooth-page-body-list'>
              {dataSource.map(items => {
                return (
                  <View
                    className='bluetooth-page-body-list-li'
                    key={items.deviceId}
                    onClick={() => createBLEConnection(items)}
                  >
                    <Icon color='#496AF2' name='link'></Icon>
                    <View className='bluetooth-page-body-list-li-text'>
                      {' '}
                      {items.localName}
                    </View>
                  </View>
                );
              })}
            </View>
          </View>
        </Cell>
      </View>
      {/* <Button onClick={() => CloseBLEConnection()} type="info">断开连接</Button>
      <Button onClick={() => writeBLECharacteristicValue('0003090000')} type="info">关机</Button> */}
    </View>
  );
});

export default inject(store => store)(BluetoothPage);
