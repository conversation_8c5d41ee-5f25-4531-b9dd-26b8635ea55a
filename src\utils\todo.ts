const getWriteBLEValue = e => {
  if (!e) return new ArrayBuffer(0);
  var t = new ArrayBuffer(e.length),
    a = new DataView(t),
    n = 0,
    r = 0,
    o = e.length;
  for (r; r < o; r += 2) {
    var i = parseInt(e.substr(r, 2), 16);
    (a.setUint8(n, i), n++);
  }
  return t;
};

const twosComplementToDecimal = twosComplementHex => {
  // 去除字符串中的前缀 "0x"
  let twosComplementString = twosComplementHex.substring(2);

  // 将十六进制字符串转换为十进制数
  let twosComplement = parseInt(twosComplementString, 16);

  // 获取补码的位数
  let bitLength = twosComplementString.length * 4;

  // 如果最高位为1，则表示为负数
  if (twosComplement & (1 << (bitLength - 1))) {
    // 将补码转换为负数的原码
    twosComplement = twosComplement - (1 << bitLength);
  }

  return twosComplement;
};

const getCurrentDate = () => {
  let currentDate = new Date();

  let year = currentDate.getFullYear(); // 获取当前年份
  let month = (currentDate.getMonth() + 1).toString().padStart(2, '0'); // 获取当前月份，并使用padStart方法补齐两位数
  let day = currentDate.getDate().toString().padStart(2, '0'); // 获取当前日期，并使用padStart方法补齐两位数
  let hours = currentDate.getHours().toString().padStart(2, '0'); // 获取当前小时，并使用padStart方法补齐两位数
  let minutes = currentDate.getMinutes().toString().padStart(2, '0'); // 获取当前分钟，并使用padStart方法补齐两位数

  let formattedDate = `${year}-${month}-${day} ${hours}:${minutes}`;
  return formattedDate;
};

export { getWriteBLEValue, twosComplementToDecimal, getCurrentDate };
