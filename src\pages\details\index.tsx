import React, { useState, useEffect } from 'react';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { Cell, CellGroup } from '@nutui/nutui-react-taro';
import { inject, observer } from 'mobx-react';
import './index.scss';
import { getNotifyStatus } from '@/config/notify';
import { getCurrentDate } from '@/utils/todo';
import { configure } from 'mobx';

configure({
  enforceActions: 'never', // 关闭严格模式
});

const DetailsPage = observer(({ store }) => {
  const counterStore = store.counterStore.state;

  const [state, setState] = useState<any>({
    checkedAsync: false,
    dataSource: [
      {
        label: '接收时间',
        value: getCurrentDate(),
      },
      {
        label: '电池编号',
        value: 'ZXXD-2003200030001',
      },
    ],
    column: [
      {
        title: '基础数据',
        dataSource: [
          {
            label: '电池总电压',
            value: '65V',
          },
          {
            label: '电流',
            value: '15A',
          },
          {
            label: '剩余容量百分比',
            value: '60%',
          },
          {
            label: '累积循环放电次数',
            value: '20次',
          },
          {
            label: '预计可用时间',
            value: '67h',
          },
          {
            label: '系统软件版本',
            value: '1.0.0',
          },
        ],
      },
      {
        title: '状态',
        dataSource: [
          {
            label: '电池状态',
            value: '正常',
          },
          {
            label: '充电 MOS 状态',
            value: '正常',
          },
          {
            label: '放电 MOS 状态',
            value: '正常',
          },
          {
            label: '强启状态',
            value: '未开启',
          },
          {
            label: '智能加热状态',
            value: '未开启',
          },
          {
            label: '加热功能状态',
            value: '未开启',
          },
        ],
      },
      {
        title: '温度',
        dataSource: [
          {
            label: '电芯温度',
            value: '10℃',
          },
          {
            label: '箱体内部温度',
            value: '8℃',
          },
          {
            label: '充放电MOS温度',
            value: '7℃',
          },
          {
            label: '加热片1温度',
            value: '0℃',
          },
          {
            label: '加热片2温度',
            value: '0℃',
          },
        ],
      },
      {
        title: '电芯单体电压',
        dataSource: [
          {
            label: '第1节电芯电压',
            value: '10V',
          },
          {
            label: '第2节电芯电压',
            value: '33V',
          },
          {
            label: '第3节电芯电压',
            value: '0V',
          },
          {
            label: '第4节电芯电压',
            value: '0V',
          },
          {
            label: '第5节电芯电压',
            value: '0V',
          },
          {
            label: '第6节电芯电压',
            value: '0V',
          },
          {
            label: '第7节电芯电压',
            value: '0V',
          },
          {
            label: '第8节电芯电压',
            value: '0V',
          },
        ],
      },
    ],
  });

  useEffect(() => {
    if (!counterStore.readId && store.BluetoothStore.state['电池编号'] == '') {
      Taro.redirectTo({
        url: '/pages/bluetooth/index',
      });
    }
  }, [store.counterStore.state]);

  useEffect(() => {
    const BluetoothStore = store.BluetoothStore.state;
    let notifyNum = 0;
    let flag = 0;
    BluetoothStore['电池电压'].map(item => {
      notifyNum += +item;
      if (item > 4.5) {
        flag = 1;
      }
    });

    setState({
      dataSource: [
        {
          label: '接收时间',
          value: getCurrentDate(),
        },
        {
          label: '电池编号',
          value: BluetoothStore['电池编号'],
        },
      ],
      column: [
        {
          title: '基础数据',
          dataSource: [
            {
              label: '电池总电压',
              value: `${flag == 1 ? '异常' : Number(notifyNum).toFixed(2)}V`,
            },
            {
              label: '电流',
              value: `${BluetoothStore['电流']}A`,
            },
            {
              label: '剩余容量百分比',
              value: `${BluetoothStore['剩余电量']}%`,
            },
            // {
            //   label: '累积循环放电次数',
            //   value: `${BluetoothStore['累计循环放电次数']}次`
            // },
            // {
            //   label: '预计可用时间',
            //   value: BluetoothStore['预计使用时间']
            // },
            // {
            //   label: '系统软件版本',
            //   value: BluetoothStore['软件版本']
            // }
          ],
        },
        {
          title: '状态',
          dataSource: [
            {
              label: '电池状态',
              value:
                getNotifyStatus[BluetoothStore['电池状态']] || '设备未连接',
            },
            // {
            //   label: '充电 MOS 状态',
            //   value: !Taro.getStorageSync('seqNo')  ? '--' : +BluetoothStore['硬件状态'][4] ? '开' : '关'
            // },
            // {
            //   label: '放电 MOS 状态',
            //   value: !Taro.getStorageSync('seqNo')  ? '--' : +BluetoothStore['硬件状态'][3] ? '开' : '关'
            // },
            {
              label: '强启状态',
              value: !Taro.getStorageSync('seqNo')
                ? '--'
                : +BluetoothStore['硬件状态'][2]
                  ? '开'
                  : '关',
            },
            // {
            //   label: '加热片MOS状态',
            //   value: !Taro.getStorageSync('seqNo')  ? '--' : +BluetoothStore['硬件状态'][1] ? '开' : '关'
            // },
            {
              label: '智能加热功能',
              value: !Taro.getStorageSync('seqNo')
                ? '--'
                : +BluetoothStore['硬件状态'][0]
                  ? '开'
                  : '关',
            },
          ],
        },
        {
          title: '温度',
          dataSource: [
            {
              label: '电芯1',
              value: `${BluetoothStore['温度'][6] == 200 || BluetoothStore['温度'][6] == undefined ? '--' : BluetoothStore['温度'][6]}℃`,
            },
            {
              label: '电芯2',
              value: `${BluetoothStore['温度'][5] == 200 || BluetoothStore['温度'][5] == undefined ? '--' : BluetoothStore['温度'][5]}℃`,
            },
            {
              label: '电芯3',
              value: `${BluetoothStore['温度'][4] == 200 || BluetoothStore['温度'][4] == undefined ? '--' : BluetoothStore['温度'][4]}℃`,
            },
            {
              label: '电芯4',
              value: `${BluetoothStore['温度'][3] == 200 || BluetoothStore['温度'][3] == undefined ? '--' : BluetoothStore['温度'][3]}℃`,
            },
            {
              label: '充放电MOS',
              value: `${BluetoothStore['温度'][2] == 200 || BluetoothStore['温度'][2] == undefined ? '--' : BluetoothStore['温度'][2]}℃`,
            },
            {
              label: '加热片',
              value: `${BluetoothStore['温度'][1] == 200 || BluetoothStore['温度'][1] == undefined ? '--' : BluetoothStore['温度'][1]}℃`,
            },
            // {
            //   label: 'MCU',
            //   value: `${BluetoothStore['温度'][0] == 200 || BluetoothStore['温度'][0] ==undefined? '--' : BluetoothStore['温度'][0]}℃`
            // }
          ],
        },
        {
          title: '电芯单体电压',
          dataSource: BluetoothStore['电池电压']
            .reverse()
            .map((item, index) => {
              return {
                label: `第${index + 1}节电芯电压`,
                value: `${item > 4.5 ? '--' : item}V`,
              };
            }),
        },
      ],
    });
    // if (counterStore.readId) {
    //   Taro.setBLEMTU({
    //     ...counterStore,
    //     mtu: 108,
    //     success: (res) => {
    //       console.log('success', res)
    //       notifyBLECharacteristicValueChange(counterStore);
    //     },
    //     fail: (res) => {
    //       console.log('fail', res)
    //     },
    //   })
    // }
    //
  }, [store.BluetoothStore.state]);

  const notifyBLECharacteristicValueChange = counterStore => {
    Taro.notifyBLECharacteristicValueChange({
      // 启用监听设备特征值变化
      state: true, // 启用 notify 功能
      ...counterStore,
      characteristicId: counterStore.readId,
      success(res) {
        console.log(res, '启用监听成功');
        onBLECharacteristicValueChange();
      },
      fail: err => {
        console.log(err, '启用监听失败');
      },
    });
  };

  const onBLECharacteristicValueChange = () => {
    // ArrayBuffer转16进制字符串示例
    function ab2hex(buffer) {
      let hexArr = Array.prototype.map.call(
        new Uint8Array(buffer),
        function (bit) {
          return ('00' + bit.toString(16)).slice(-2);
        }
      );
      return hexArr.join('');
    }
    Taro.onBLECharacteristicValueChange(res => {
      try {
        // const date = ab2hex(res.value)
        // const BluetoothStore = getParseBluetoothStore(date)
      } catch (err) {
        console.log('转换出错了', err);
      }
    });
  };

  const onShareAppMessage = res => {
    return {
      title: '车京途5G智能锂电',
      path: '/pages/product/index',
    };
  };

  useEffect(() => {
    console.log(store.BluetoothStore.state, '数据是怎么样的？');
  }, []);
  return (
    <View className='details-page'>
      {state.dataSource.map(items => {
        return <Cell title={items.label} desc={items.value} />;
      })}
      {state.column.map(item => {
        return (
          <CellGroup title={item.title} key={item.title}>
            {item.dataSource.map(items => {
              return <Cell title={items.label} desc={items.value} />;
            })}
          </CellGroup>
        );
      })}
    </View>
  );
});

export default inject(store => store)(DetailsPage);
