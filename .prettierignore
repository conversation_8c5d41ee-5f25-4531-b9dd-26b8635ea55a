# Dependencies
node_modules/

# Build outputs
dist/
build/

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# next.js build output
.next

# Nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Taro specific
project.config.json
project.private.config.json
project.tt.json

# Lock files
package-lock.json
yarn.lock
