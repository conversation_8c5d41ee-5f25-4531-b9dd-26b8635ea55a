.register-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f6f6f6;
  padding: 40px;

  .logo-container {
    margin-bottom: 40px;
    text-align: center;

    .logo {
      width: 201rpx;
      border-radius: 50%;
      box-shadow: 1rpx 2rpx 16rpx 11rpx rgb(230, 230, 230);
      background-color: white;
    }
  }

  .agreement {
    display: flex;
    align-items: center;
    margin-top: 30px;
    font-size: 10px;

    .agreement-text {
      margin-left: 10px;
    }

    .link {
      color: #44bdb4;
      text-decoration: underline;
    }
  }

  .button-container {
    width: 100%;
    max-width: 300px;
    &-title {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin-bottom: 30px;
      font-size: 15px;
      font-weight: 600;
      &-item {
        font-size: 15px;
        font-weight: 400;
      }
    }
    .auth-button {
      background-color: #3b81ff;
      border: none;
      border-radius: 25px;
      height: 50px;
      font-size: 16px;
      margin-bottom: 15px;

      &:disabled {
        background-color: #6a8bc7;
        opacity: 0.7;
      }
    }

    .skip-button {
      background-color: transparent;
      border: 1px solid black;
      border-radius: 25px;
      height: 50px;
      font-size: 16px;
    }
  }
}
