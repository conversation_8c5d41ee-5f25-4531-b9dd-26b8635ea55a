import React, { useEffect } from 'react';
import { View, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

const SplashPage = () => {
  useEffect(() => {
    const timer = setTimeout(() => {
      Taro.switchTab({
        url: '/pages/home/<USER>',
      });
    }, 3000); // 2秒后跳转

    return () => clearTimeout(timer);
  }, []);

  return (
    <View className='splash-container'>
      <Image
        className='splash-logo'
        src={require('@/assets/images/LOGO1.png')}
      />
      <View className='splash-animation'></View>
    </View>
  );
};

export default SplashPage;
