export default defineAppConfig({
  requiredPrivateInfos: ['getLocation'],
  pages: [
    'pages/splash/index',
    'pages/home/<USER>',
    'pages/product/index',
    'pages/bluetooth/index',
    'pages/details/index',
    'pages/user/index',
    'pages/register/index',
    'pages/map/index',
    'pages/userPage/myDevice/index',
    'pages/userPage/myStruction/index',
    'pages/userPage/myElectronic/index',
    'pages/userPage/publicize/index',
    'pages/userPage/registration/index',
    'pages/userPage/account/index',
    'pages/userPage/about/index',
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black',
  },
  tabBar: {
    custom: true,
    color: '#666666',
    selectedColor: '#3b81ff',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    list: [
      {
        text: '首页',
        pagePath: 'pages/home/<USER>',
        iconPath: 'assets/images/tabs/home.png',
        selectedIconPath: 'assets/images/tabs/home_selected.png',
      },
      {
        text: '产品介绍',
        pagePath: 'pages/product/index',
        iconPath: 'assets/images/tabs/product.png',
        selectedIconPath: 'assets/images/tabs/product_selected.png',
      },
      {
        text: '详情',
        pagePath: 'pages/details/index',
        iconPath: 'assets/images/tabs/details.png',
        selectedIconPath: 'assets/images/tabs/details_selected.png',
      },
      {
        text: '我的',
        pagePath: 'pages/user/index',
        iconPath: 'assets/images/tabs/user.png',
        selectedIconPath: 'assets/images/tabs/user_selected.png',
      },
    ],
  },
  permission: {
    'scope.userLocation': {
      desc: '需要获取您的位置信息以显示当前位置',
    },
    'scope.bluetooth': {
      desc: '需要获取您的蓝牙授权以连接蓝牙设备。',
    },
    'scope.writePhotosAlbum': {
      desc: '需要获取您的相册授权以来保存操作资料。。',
    },
  },
});
