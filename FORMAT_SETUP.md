# 代码格式化设置说明

本项目已经配置了自动代码格式化功能，包括保存时自动格式化和手动格式化命令。

## 已配置的功能

### 1. 自动格式化（保存时）
- 在 VSCode 中保存文件时会自动格式化代码
- 支持的文件类型：JavaScript、TypeScript、React、JSON、CSS、SCSS、HTML、Markdown

### 2. 手动格式化命令
```bash
# 格式化所有源代码文件
npm run format

# 检查代码格式（不修改文件）
npm run format:check

# 运行 ESLint 检查
npm run lint

# 运行 ESLint 并自动修复
npm run lint:fix
```

## 配置文件说明

### Prettier 配置 (`.prettierrc`)
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf",
  "jsxSingleQuote": true,
  "bracketSameLine": false
}
```

### VSCode 设置 (`.vscode/settings.json`)
- 启用保存时自动格式化
- 设置 Prettier 为默认格式化工具
- 配置 ESLint 自动修复

### 忽略文件 (`.prettierignore`)
- 排除 `node_modules`、`dist`、构建文件等

## CSS 冲突问题解决

项目中解决了 NutUI 组件库导致的 CSS 模块冲突问题：
- 在 webpack 配置中设置 `miniCssExtractPluginOption.ignoreOrder: true`
- 统一了样式导入顺序

## VSCode 扩展推荐

项目推荐安装以下 VSCode 扩展（`.vscode/extensions.json`）：
- Prettier - Code formatter
- ESLint
- Tailwind CSS IntelliSense
- TypeScript and JavaScript Language Features

## 使用说明

1. **首次设置**：
   - 确保安装了推荐的 VSCode 扩展
   - 重启 VSCode 以应用设置

2. **日常开发**：
   - 保存文件时会自动格式化
   - 可以使用 `npm run format` 手动格式化所有文件
   - 使用 `npm run lint` 检查代码质量

3. **团队协作**：
   - 所有团队成员使用相同的格式化配置
   - 提交代码前运行 `npm run format:check` 确保格式一致

## 故障排除

如果自动格式化不工作：
1. 检查是否安装了 Prettier 扩展
2. 检查 VSCode 设置是否正确加载
3. 重启 VSCode
4. 确保项目根目录有 `.prettierrc` 文件

如果遇到 CSS 冲突错误：
- 项目已经配置了解决方案，正常情况下不会出现此问题
- 如果仍有问题，检查 `config/dev.js` 和 `config/prod.js` 中的 `miniCssExtractPluginOption` 配置
