import React, { useEffect, useState, useRef } from 'react';
import Taro, { setStorageSync } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { Input, Button, Checkbox, Cell } from '@nutui/nutui-react-taro';
import {
  setBladeValidete,
  postBladeSystemUserSave,
  getBladeSystemUserCkEquNoExist,
} from '../../../api/index';
import './index.scss';
import { getCurrentInstance } from '@tarojs/runtime';
import { Icon } from '@nutui/nutui-react-taro';

function registrationPage() {
  const sendingTime = useRef<any>(null);
  const sendingTimeout = useRef<any>(60);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [errorText1, seterrorText1] = useState('');
  const [errorText3, seterrorText3] = useState('');
  const [errorText2, seterrorText2] = useState('');
  const [smsCode, setSmsCode] = useState('');
  const [state, setState] = useState({
    sending: false,
    sendingTime: 60,
    loading: false,
  });

  const [form, setForm] = useState({
    userType: 2,
    account: '',
    name: '',
    realName: '',
    phone: '',
    seqNo: '',
    carModel: '',
    carNo: '',
    smsCode: '',
    smsId: '',
  });

  const stateBth = useRef({
    deviceId: '',
    characteristicId: '',
    serviceId: '',
    writeDefaultId: '',
    readId: '',
    localName: '未知',
  });

  // const getBtn = () => {
  //   console.log(!form.account , !form.realName , !form.phone , !form.seqNo , !form.smsCode)
  //   return !form.account || !form.realName || !form.phone || !form.seqNo || !form.smsCode
  // }

  const handleChange = (name, value) => {
    setForm({
      ...form,
      [name]: value,
    });
  };

  const handleSpend = () => {
    setBladeValidete({ phone: form.phone }).then(res => {
      setState({
        ...state,
        sending: true,
      });
      setForm({
        ...form,
        smsId: res.data.id,
        smsCode: res.data.phone,
      });

      setSmsCode(res.data.phone);
    });
  };

  useEffect(() => {
    if (state.sending) {
      sendingTime.current = setInterval(() => {
        sendingTimeout.current = sendingTimeout.current - 1;
        if (sendingTimeout.current === 1) {
          clearInterval(sendingTime.current);
          sendingTimeout.current = 60;
          setState({
            ...state,
            sending: false,
            sendingTime: 60,
          });
        } else {
          setState({
            ...state,
            sendingTime: sendingTimeout.current - 1,
          });
        }
      }, 1000);
    }
  }, [state.sending]);

  const handleSubmit = () => {
    console.log(form, '表单数据？');
    //这里验证

    const isValidChars = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
    const isValidLength =
      form.realName.length >= 4 && form.realName.length <= 30;
    const regex = /^BAT[A-Z0-9]{8}$/;
    if (form.smsCode === '') {
      Taro.showToast({
        title: '请输入验证码！',
        icon: 'error',
        duration: 2000,
      });
      return;
    }
    if (
      (form.realName === '' || !isValidChars.test(form.realName)) &&
      !isValidLength
    ) {
      seterrorText3('格式错误: 需要中文或数字或英文,且组成4-30 个字符');
      return;
    }

    postBladeSystemUserSave({ ...form, name: form.realName })
      .then(res => {
        setStorageSync('seqNo', form.seqNo);
        setStorageSync('phone', form.phone);
        Taro.showModal({
          title: '提示',
          content: '注册成功!',
          showCancel: false,
          success: () => {
            Taro.switchTab({
              url: '/pages/home/<USER>',
            });
          },
        });
        setSmsCode('');
        setState({
          ...state,
          loading: false,
        });
      })
      .catch(err => {
        console.log(err);
      });

    // })
  };

  const handleCancel = () => {
    Taro.switchTab({
      url: '/pages/home/<USER>',
    });
  };

  const tapScan = async () => {
    Taro.showLoading({
      title: '正在检索蓝牙及数据校验，请稍等...',
    });
    Taro.scanCode({
      success: function (res) {
        const result = res.result;
        const response = getBladeSystemUserCkEquNoExist({ equNo: res.result });
        response.then(status => {
          if (status.success) {
            setTimeout(() => {
              Taro.hideLoading();
              Promise.resolve().then(() => {
                setForm({
                  ...form,
                  seqNo: result,
                });
                // console.log(form)
                // handleBegin()
              });
            }, 3000);
          }
        });
      },
      fail(res) {
        Taro.hideLoading();
      },
    });
  };

  useEffect(() => {
    Taro.hideHomeButton();
    const relust = getCurrentInstance().router as any;
    setForm({
      ...form,
      account: relust.params.phone,
      phone: relust.params.phone,
    });
    seterrorText2(
      '(国内)车牌号格式错误！\n正确示例：\n普通车牌：京A12345\n新能源车牌：京AD12345\n'
    );
    seterrorText3('提示:  需要中文或数字或英文,且组成4-30 个字符');
  }, []);

  return (
    <View className='registration-page'>
      {/* <Input  required
        name="text" defaultValue={form.seqNo}  onChange={(e) => handleChange('seqNo', e)} label="电池编号" placeholder="请输入电池编号" errorMessage={errorText1}
        slotButton={<Icon onClick={tapScan} color="#000000" name="scan2"></Icon>}
      /> */}
      <Input
        required
        onChange={e => handleChange('realName', e)}
        name='text'
        label='用户名'
        placeholder='请输入用户名'
        errorMessage={errorText3}
      />

      {/* <Input  onChange={(e) => handleChange('carModel', e)} name="text" label="车型" placeholder="请输入车型" /> */}
      <Input
        onChange={e => handleChange('carNo', e)}
        name='text'
        label='车牌号'
        placeholder='请输入车牌号'
        errorMessage={errorText2}
      />
      <Input
        readonly
        label='手机号码'
        placeholder='请输入手机号码'
        clearable
        disabled={true}
        defaultValue={form.phone}
        slotButton={
          <Button
            onClick={handleSpend}
            disabled={state.sending}
            size='small'
            type='warning'
          >
            {state.sending ? state.sendingTime : '发送验证码'}
          </Button>
        }
      />
      <Input
        defaultValue={smsCode}
        readonly={true}
        onChange={e => handleChange('smsCode', e)}
        name='number'
        type='number'
        label='验证码'
        placeholder='请输入验证码'
      />

      <View className='registration-page-checkout'>
        <Checkbox
          iconName='success'
          iconActiveName='checked'
          textPosition='right'
          label='隐私政策'
          checked={true}
        />
      </View>
      <View className='registration-page-button'>
        <View className='registration-page-button-btn2'>
          <Button onClick={handleCancel} block type='warning'>
            游客登录
          </Button>
        </View>
        <View className='registration-page-button-btn1'>
          <Button
            loading={state.loading}
            onClick={handleSubmit}
            block
            type='primary'
          >
            确 定
          </Button>
        </View>
      </View>
    </View>
  );
}

export default registrationPage;
