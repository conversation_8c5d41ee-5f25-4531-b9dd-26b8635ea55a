import { makeAutoObservable } from 'mobx';

class RefreshStore {
  refreshCount = 0;
  batteryRefreshCount = 0;

  constructor() {
    makeAutoObservable(this);
  }

  triggerRefresh() {
    this.refreshCount++;
  }

  triggerBatteryRefresh() {
    this.batteryRefreshCount++;
  }

  deviceSwitchRefreshCount = 0;

  triggerDeviceSwitch() {
    this.deviceSwitchRefreshCount++;
  }

  clickEwmCount = 0;

  clickEwm() {
    this.clickEwmCount++;
  }

  blueToFourCount = 0;

  blueToFour() {
    this.blueToFourCount++;
  }

  blueCount = 0;
  blueToothCount() {
    this.blueCount++;
  }

  PopoverCount = 0;
  OpenPopoverCount() {
    this.PopoverCount++;
  }

  switchBlue = 0;
  switchBlueThooth() {
    this.switchBlue++;
  }

  clearInputCount = 0;
  clearInput() {
    this.clearInputCount++;
  }

  orOpenWebSocket = false;
  isOpenWebSocket(res: boolean) {
    this.orOpenWebSocket = res;
  }
}

export default new RefreshStore();
