{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.requireConfig": true, "prettier.useEditorConfig": false, "files.eol": "\n", "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true}